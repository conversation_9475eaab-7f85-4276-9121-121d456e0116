import { NextRequest, NextResponse } from 'next/server';
import { registerUser, setAuth<PERSON><PERSON>ie } from '@/lib/auth';
import { moveTempFilesToUser, TempUploadResult } from '@/lib/file-upload';
// Use the same database module as auth.ts
let dbModule;
try {
  // Check if we have database environment variables
  const hasPostgresUrl = process.env.POSTGRES_URL || process.env.DATABASE_URL;
  const isLocalhost = hasPostgresUrl && (
    hasPostgresUrl.includes('localhost') ||
    hasPostgresUrl.includes('127.0.0.1')
  );

  if (hasPostgresUrl && !isLocalhost) {
    // Use real database for remote connections (like Neon DB)
    console.log('Registration: Using real PostgreSQL database');
    dbModule = require('@/lib/db');
  } else if (isLocalhost) {
    // Use mock for localhost development
    console.log('Registration: Using mock database for localhost development');
    dbModule = require('@/lib/mock-db');
  } else {
    // No database configured, use mock
    console.log('Registration: No database URL configured, using mock database');
    dbModule = require('@/lib/mock-db');
  }
} catch (error) {
  console.log('Registration: Database connection failed, using mock database for development:', error.message);
  dbModule = require('@/lib/mock-db');
}

const {
  createUserProfile,
  createEmergencyContacts,
  createPhysicalHealth,
  createMedications,
  createMentalHealth,
  createHarassmentExperiences,
  initializeDatabase
} = dbModule;
import { CompleteRegistrationData, ApiResponse } from '@/lib/types';

export async function POST(request: NextRequest) {
  try {
    // Initialize database tables if they don't exist
    await initializeDatabase();

    const registrationData: CompleteRegistrationData = await request.json();

    // Debug: Log the received data
    console.log('=== REGISTRATION DEBUG ===');
    console.log('Received registration data:', JSON.stringify(registrationData, null, 2));
    console.log('Step1 data:', registrationData.step1);
    console.log('Step1 fullName:', registrationData.step1?.fullName);
    console.log('Step1 email:', registrationData.step1?.email);
    console.log('Step1 password:', registrationData.step1?.password ? '[PRESENT]' : '[MISSING]');
    console.log('========================');

    // Validate required data
    if (!registrationData.step1) {
      console.log('ERROR: Missing step1 data');
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Missing user authentication data'
      }, { status: 400 });
    }

    const { step1, step2, step3, step4, step5 } = registrationData;

    // Validate step 1 (authentication data)
    if (!step1.fullName || !step1.email || !step1.password) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Missing required fields: fullName, email, password'
      }, { status: 400 });
    }

    if (step1.password !== step1.confirmPassword) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Passwords do not match'
      }, { status: 400 });
    }

    // Register the user (this will validate email and password)
    const user = await registerUser({
      email: step1.email,
      password: step1.password,
      fullName: step1.fullName
    });

    console.log('User registered successfully, processing files...');

    // Process uploaded files if they exist
    let profilePhotoUrl: string | undefined;
    let identityDocumentUrl: string | undefined;

    // Handle profile photo and identity document from step1
    const tempFiles: TempUploadResult[] = [];

    if (step1.profilePhoto && typeof step1.profilePhoto === 'object' && 'tempId' in step1.profilePhoto) {
      tempFiles.push(step1.profilePhoto as TempUploadResult);
    }

    if (step1.identityDocument && typeof step1.identityDocument === 'object' && 'tempId' in step1.identityDocument) {
      tempFiles.push(step1.identityDocument as TempUploadResult);
    }

    if (tempFiles.length > 0) {
      console.log('Moving temp files to permanent location...');
      const permanentFiles = await moveTempFilesToUser(tempFiles, user.id.toString());

      // Map files to their purposes
      for (let i = 0; i < tempFiles.length; i++) {
        const tempFile = tempFiles[i];
        const permanentFile = permanentFiles[i];

        if (tempFile.uploadType === 'profile') {
          profilePhotoUrl = permanentFile?.url;
        } else if (tempFile.uploadType === 'identity') {
          identityDocumentUrl = permanentFile?.url;
        }
      }
    }

    // Create user profile if step2 data exists and has meaningful data
    if (step2) {
      const hasStep2Data = step2.age || step2.gender || step2.address || step2.educationLevel ||
                          step2.occupation || step2.hobbies || step2.frequentPlaces ||
                          profilePhotoUrl || identityDocumentUrl;

      if (hasStep2Data) {
        console.log('Creating user profile with step2 data...');
        await createUserProfile({
          userId: user.id,
          age: step2.age ? parseInt(step2.age) : undefined,
          gender: step2.gender || undefined,
          sexualOrientation: step2.sexualOrientation || undefined,
          address: step2.address || undefined,
          educationLevel: step2.educationLevel || undefined,
          occupation: step2.occupation || undefined,
          hobbies: step2.hobbies || undefined,
          frequentPlaces: step2.frequentPlaces || undefined,
          identityDocumentUrl: identityDocumentUrl,
          profilePhotoUrl: profilePhotoUrl
        });
        console.log('User profile created successfully');
      } else {
        console.log('Skipping user profile creation - no meaningful data provided');
      }

      // Create emergency contacts only if they have valid data
      if (step2.emergencyContacts && step2.emergencyContacts.length > 0) {
        const validContacts = step2.emergencyContacts.filter(contact =>
          contact.name?.trim() && contact.relationship?.trim() && contact.phone?.trim()
        );

        if (validContacts.length > 0) {
          console.log(`Creating ${validContacts.length} emergency contacts...`);
          await createEmergencyContacts(user.id, validContacts);
          console.log('Emergency contacts created successfully');
        } else {
          console.log('Skipping emergency contacts - no valid contacts provided');
        }
      }
    }

    // Create physical health data if step3 exists and has meaningful data
    if (step3) {
      const hasStep3Data = step3.weight || step3.height || step3.bloodType ||
                          step3.hasDisability || step3.disabilityDescription ||
                          step3.chronicConditions || step3.allergies?.medical ||
                          step3.allergies?.food || step3.allergies?.environmental;

      if (hasStep3Data) {
        console.log('Creating physical health record...');
        await createPhysicalHealth({
          userId: user.id,
          weight: step3.weight ? parseFloat(step3.weight) : undefined,
          height: step3.height ? parseFloat(step3.height) : undefined,
          bloodType: step3.bloodType || undefined,
          hasDisability: step3.hasDisability || false,
          disabilityDescription: step3.disabilityDescription || undefined,
          chronicConditions: step3.chronicConditions || undefined,
          medicalAllergies: step3.allergies?.medical || undefined,
          foodAllergies: step3.allergies?.food || undefined,
          environmentalAllergies: step3.allergies?.environmental || undefined
        });
        console.log('Physical health record created successfully');
      } else {
        console.log('Skipping physical health creation - no meaningful data provided');
      }

      // Create medications only if they have valid data
      if (step3.currentMedications && step3.currentMedications.length > 0) {
        const validMedications = step3.currentMedications.filter(med =>
          med.name?.trim()
        );

        if (validMedications.length > 0) {
          console.log(`Creating ${validMedications.length} general medications...`);
          await createMedications(user.id, validMedications.map(med => ({
            ...med,
            type: 'general'
          })));
          console.log('Medications created successfully');
        } else {
          console.log('Skipping medications - no valid medications provided');
        }
      }
    }

    // Create mental health data if step4 exists and has meaningful data
    if (step4) {
      const hasStep4Data = step4.psychiatricConditions || step4.hasAnxietyAttacks ||
                          step4.anxietyFrequency || step4.familyHistory;

      if (hasStep4Data) {
        console.log('Creating mental health record...');
        await createMentalHealth({
          userId: user.id,
          psychiatricConditions: step4.psychiatricConditions || undefined,
          hasAnxietyAttacks: step4.hasAnxietyAttacks || false,
          anxietyFrequency: step4.anxietyFrequency || undefined,
          familyHistory: step4.familyHistory || undefined
        });
        console.log('Mental health record created successfully');
      } else {
        console.log('Skipping mental health creation - no meaningful data provided');
      }

      // Create psychiatric medications only if they have valid data
      if (step4.psychiatricMedications && step4.psychiatricMedications.length > 0) {
        const validPsychMedications = step4.psychiatricMedications.filter(med =>
          med.name?.trim()
        );

        if (validPsychMedications.length > 0) {
          console.log(`Creating ${validPsychMedications.length} psychiatric medications...`);
          await createMedications(user.id, validPsychMedications.map(med => ({
            ...med,
            type: 'psychiatric'
          })));
          console.log('Psychiatric medications created successfully');
        } else {
          console.log('Skipping psychiatric medications - no valid medications provided');
        }
      }
    }

    // Create harassment experiences if step5 exists
    if (step5 && step5.experiences && step5.experiences.length > 0) {
      const validExperiences = step5.experiences.filter(exp =>
        exp.category && exp.description
      );

      if (validExperiences.length > 0) {
        // Process evidence files for each experience
        const experiencesWithFiles = await Promise.all(
          validExperiences.map(async (exp) => {
            let evidenceFiles: Array<{
              fileUrl: string;
              fileName: string;
              fileType: string;
              fileSize: number;
            }> = [];

            if (exp.evidence && exp.evidence.length > 0) {
              // Filter for temp upload results
              const tempEvidenceFiles = exp.evidence.filter(
                (file): file is TempUploadResult =>
                  typeof file === 'object' && 'tempId' in file
              ) as TempUploadResult[];

              if (tempEvidenceFiles.length > 0) {
                const permanentEvidenceFiles = await moveTempFilesToUser(
                  tempEvidenceFiles,
                  user.id.toString()
                );
                evidenceFiles = permanentEvidenceFiles;
              }
            }

            return {
              ...exp,
              evidenceFiles
            };
          })
        );

        await createHarassmentExperiences(user.id, experiencesWithFiles);
      }
    }

    // Generate login token for the new user
    console.log('Generating login token for user:', step1.email);
    const { token } = await import('@/lib/auth').then(auth =>
      auth.loginUser(step1.email, step1.password)
    );
    console.log('Login token generated:', token ? 'SUCCESS' : 'FAILED');

    // Set auth cookie using NextResponse
    console.log('Setting auth cookie...');

    const response = NextResponse.json<ApiResponse>({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          fullName: user.full_name,
          isVerified: false
        },
        token
      },
      message: 'Registration completed successfully'
    });

    // Set the cookie in the response
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/'
    });

    console.log('Auth cookie set in response');
    return response;

  } catch (error: any) {
    console.error('Registration error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: error.message || 'Registration failed'
    }, { status: 500 });
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
